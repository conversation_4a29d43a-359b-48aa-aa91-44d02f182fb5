/**
 * ===============================================================================
 * MODULE: Module_InvoiceStructure
 * DESCRIPTION: Handles the creation, formatting, and layout of the invoice sheet,
 *              as well as its core formulas and data population logic.
 * ===============================================================================
 */

// ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
// 📋 WORKSHEET CREATION FUNCTIONS
// ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

function CreateInvoiceSheet() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  
  try {
    // Try to get the sheet
    let sheet = spreadsheet.getSheetByName("GST_Tax_Invoice_for_interstate");
    
    // If the sheet doesn't exist, create it. If it exists, clear it completely.
    if (!sheet) {
      sheet = spreadsheet.insertSheet("GST_Tax_Invoice_for_interstate");
    } else {
      // Complete cleanup of existing sheet
      sheet.clear();
      sheet.clearFormats();
    }

    // Set column widths
    sheet.setColumnWidth(1, 60);   // Column A - Sr.No.
    sheet.setColumnWidth(2, 144);  // Column B - Description of Goods/Services
    sheet.setColumnWidth(3, 144);  // Column C - HSN/SAC Code
    sheet.setColumnWidth(4, 108);  // Column D - Quantity
    sheet.setColumnWidth(5, 84);   // Column E - UOM
    sheet.setColumnWidth(6, 120);  // Column F - Rate
    sheet.setColumnWidth(7, 168);  // Column G - Amount
    sheet.setColumnWidth(8, 120);  // Column H - Taxable Value
    sheet.setColumnWidth(9, 72);   // Column I - IGST Rate
    sheet.setColumnWidth(10, 120); // Column J - IGST Amount
    sheet.setColumnWidth(11, 144); // Column K - Total Amount

    // Set default font for all cells
    const range = sheet.getRange(1, 1, sheet.getMaxRows(), sheet.getMaxColumns());
    range.setFontFamily("Segoe UI")
         .setFontSize(11)
         .setFontColor("#1a1a1a");

    // Create header sections with premium professional styling
    CreateHeaderRow(sheet, 1, "A1:K1", "ORIGINAL", 12, true, "#2F5061", "#FFFFFF", 25);
    CreateHeaderRow(sheet, 2, "A2:K2", "KAVERI TRADERS", 24, true, "#2F5061", "#FFFFFF", 37);
    CreateHeaderRow(sheet, 3, "A3:K3", "191, Guduru, Pagadalapalli, Idulapalli, Tirupati, Andhra Pradesh - 524409", 11, true, "#F5F5F5", "#1a1a1a", 27);
    CreateHeaderRow(sheet, 4, "A4:K4", "GSTIN: 37HERPB7733F1Z5", 14, true, "#F5F5F5", "#1a1a1a", 27);
    CreateHeaderRow(sheet, 5, "A5:K5", "Email: <EMAIL>", 11, true, "#F5F5F5", "#1a1a1a", 25);

    // Row 6: TAX-INVOICE header
    CreateHeaderRow(sheet, 6, "A6:G6", "TAX-INVOICE", 22, true, "#F0F0F0", "#000000", 28);
    CreateHeaderRow(sheet, 6, "H6:K6", "Original for Recipient\nDuplicate for Supplier/Transporter\nTriplicate for Supplier", 9, true, "#FAFAFA", "#000000", 45);

    // Enable text wrapping for the right section and ensure center alignment for TAX-INVOICE
    sheet.getRange("A6:G6").setHorizontalAlignment("center").setVerticalAlignment("middle");
    sheet.getRange("H6:K6").setWrap(true);

    // Create invoice details section
    CreateInvoiceDetailsSection(sheet);
    
    // Create party details section
    CreatePartyDetailsSection(sheet);
    
    // Create item table section
    CreateItemTableSection(sheet);
    
    // Create tax summary section
    CreateTaxSummarySection(sheet);
    
    // Create bottom section
    CreateBottomSection(sheet);
    
    // Create buttons
    CreateInvoiceButtons(sheet);
    
    // Auto-populate initial fields
    AutoPopulateInvoiceFields(sheet);

  } catch (error) {
    SpreadsheetApp.getUi().alert('Error', 'Error creating invoice sheet: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}

function CreateHeaderRow(sheet, row, range, text, fontSize, bold, bgColor, fontColor, height) {
  try {
    const headerRange = sheet.getRange(range);
    headerRange.merge()
             .setValue(text)
             .setFontSize(fontSize)
             .setFontWeight(bold ? "bold" : "normal")
             .setBackground(bgColor)
             .setFontColor(fontColor)
             .setHorizontalAlignment("center")
             .setVerticalAlignment("middle")
             .setBorder(true, true, true, true, false, false, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);
    
    sheet.setRowHeight(row, height);
  } catch (error) {
    console.error('Error creating header row:', error);
  }
}

function CreateInvoiceDetailsSection(sheet) {
  try {
    // Row 7: Invoice No., Transport Mode, Challan No.
    sheet.getRange("A7:B7").merge().setValue("Invoice No.")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("C7").setValue("")
         .setFontWeight("bold").setFontColor("#DC143C")
         .setHorizontalAlignment("center").setVerticalAlignment("middle");

    sheet.getRange("D7:E7").merge().setValue("Transport Mode")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("F7:G7").merge().setValue("By Lorry")
         .setHorizontalAlignment("left");

    sheet.getRange("H7:I7").merge().setValue("Challan No.")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("J7:K7").merge().setValue("")
         .setHorizontalAlignment("left");

    // Row 8: Invoice Date, Vehicle Number, Transporter Name
    sheet.getRange("A8:B8").merge().setValue("Invoice Date")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("C8").setValue("")
         .setFontWeight("bold").setHorizontalAlignment("left");

    sheet.getRange("D8:E8").merge().setValue("Vehicle Number")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("F8:G8").merge().setValue("")
         .setHorizontalAlignment("left");

    sheet.getRange("H8:I8").merge().setValue("Transporter Name")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("J8:K8").merge().setValue("NARENDRA")
         .setHorizontalAlignment("left");

    // Row 9: State, Date of Supply, L.R Number
    sheet.getRange("A9:B9").merge().setValue("State")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("C9").setValue("Andhra Pradesh")
         .setHorizontalAlignment("left").setFontSize(10);

    sheet.getRange("D9:E9").merge().setValue("Date of Supply")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("F9:G9").merge().setValue("")
         .setHorizontalAlignment("left");

    sheet.getRange("H9:I9").merge().setValue("L.R Number")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("J9:K9").merge().setValue("")
         .setHorizontalAlignment("left");

    // Row 10: State Code, Place of Supply, P.O Number
    sheet.getRange("A10:B10").merge().setValue("State Code")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("C10").setValue("37")
         .setHorizontalAlignment("left");

    sheet.getRange("D10:E10").merge().setValue("Place of Supply")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("F10:G10").merge().setValue("")
         .setHorizontalAlignment("left");

    sheet.getRange("H10:I10").merge().setValue("P.O Number")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");
    
    sheet.getRange("J10:K10").merge().setValue("")
         .setHorizontalAlignment("left");

    // Apply borders and formatting with professional color
    sheet.getRange("A7:K10").setBorder(true, true, true, true, true, true, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);
    
    for (let i = 7; i <= 10; i++) {
      sheet.setRowHeight(i, 28);
    }
  } catch (error) {
    console.error('Error creating invoice details section:', error);
  }
}

function CreatePartyDetailsSection(sheet) {
  try {
    // Party Details Headers
    CreateHeaderRow(sheet, 11, "A11:F11", "Details of Receiver (Billed to)", 11, true, "#F5F5F5", "#1a1a1a", 26);
    CreateHeaderRow(sheet, 11, "G11:K11", "Details of Consignee (Shipped to)", 11, true, "#F5F5F5", "#1a1a1a", 26);

    // Set center alignment for row 11 content
    sheet.getRange("A11:F11").setHorizontalAlignment("center").setVerticalAlignment("middle");
    sheet.getRange("G11:K11").setHorizontalAlignment("center").setVerticalAlignment("middle");

    // Create party detail fields (rows 12-16)
    const partyFields = [
      { row: 12, label: "Name:" },
      { row: 13, label: "Address:" },
      { row: 14, label: "GSTIN:" },
      { row: 15, label: "State:" },
      { row: 16, label: "State Code:" }
    ];

    partyFields.forEach(field => {
      // Receiver section
      sheet.getRange(`A${field.row}:B${field.row}`).merge().setValue(field.label)
           .setFontWeight("bold").setHorizontalAlignment("left")
           .setBackground("#F5F5F5").setFontColor("#1a1a1a");
      
      sheet.getRange(`C${field.row}:F${field.row}`).merge().setValue("")
           .setHorizontalAlignment("left");

      // Consignee section
      sheet.getRange(`G${field.row}:H${field.row}`).merge().setValue(field.label)
           .setFontWeight("bold").setHorizontalAlignment("left")
           .setBackground("#F5F5F5").setFontColor("#1a1a1a");
      
      sheet.getRange(`I${field.row}:K${field.row}`).merge().setValue("")
           .setHorizontalAlignment("left");
    });

    // Apply borders and formatting
    sheet.getRange("A11:K16").setBorder(true, true, true, true, true, true, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);
    
    for (let i = 11; i <= 16; i++) {
      sheet.setRowHeight(i, 26);
    }
  } catch (error) {
    console.error('Error creating party details section:', error);
  }
}

function CreateItemTableSection(sheet) {
  try {
    // Item table headers (row 17)
    const itemHeaders = [
      { col: "A", text: "Sr.No.", width: 60 },
      { col: "B", text: "Description of Goods/Services", width: 144 },
      { col: "C", text: "HSN/SAC Code", width: 144 },
      { col: "D", text: "Quantity", width: 108 },
      { col: "E", text: "UOM", width: 84 },
      { col: "F", text: "Rate", width: 120 },
      { col: "G", text: "Amount", width: 168 },
      { col: "H", text: "Taxable Value", width: 120 },
      { col: "I", text: "IGST Rate (%)", width: 72 },
      { col: "J", text: "IGST Amount", width: 120 },
      { col: "K", text: "Total Amount", width: 144 }
    ];

    itemHeaders.forEach(header => {
      sheet.getRange(`${header.col}17`).setValue(header.text)
           .setFontWeight("bold").setFontSize(10)
           .setBackground("#2F5061").setFontColor("#FFFFFF")
           .setHorizontalAlignment("center").setVerticalAlignment("middle")
           .setWrap(true);
    });

    // Create item rows (18-21)
    for (let row = 18; row <= 21; row++) {
      const srNo = row - 17; // Sr.No. starts from 1
      sheet.getRange(`A${row}`).setValue(srNo)
           .setHorizontalAlignment("center").setVerticalAlignment("middle");

      // Set formulas for calculated columns
      sheet.getRange(`G${row}`).setFormula(`=IF(AND(D${row}<>"",F${row}<>""),D${row}*F${row},"")`); // Amount = Quantity * Rate
      sheet.getRange(`H${row}`).setFormula(`=G${row}`); // Taxable Value = Amount
      sheet.getRange(`J${row}`).setFormula(`=IF(AND(H${row}<>"",I${row}<>""),H${row}*I${row}/100,"")`); // IGST Amount
      sheet.getRange(`K${row}`).setFormula(`=IF(H${row}<>"",H${row}+J${row},"")`); // Total Amount
    }

    // Apply borders and formatting to item table
    sheet.getRange("A17:K21").setBorder(true, true, true, true, true, true, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);
    sheet.setRowHeight(17, 35); // Header row height

    for (let i = 18; i <= 21; i++) {
      sheet.setRowHeight(i, 28);
    }
  } catch (error) {
    console.error('Error creating item table section:', error);
  }
}

function CreateTaxSummarySection(sheet) {
  try {
    // Tax summary section (rows 22-28)
    const taxSummaryData = [
      { row: 22, label: "Total Before Tax", formula: "=SUM(H18:H21)" },
      { row: 23, label: "CGST", formula: "" },
      { row: 24, label: "SGST", formula: "" },
      { row: 25, label: "IGST", formula: "=SUM(J18:J21)" },
      { row: 26, label: "Round Off", formula: "" },
      { row: 27, label: "Total Tax Amount", formula: "=K25" },
      { row: 28, label: "Total Amount", formula: "=K22+K27" }
    ];

    taxSummaryData.forEach(item => {
      sheet.getRange(`I${item.row}:J${item.row}`).merge().setValue(item.label)
           .setFontWeight("bold").setHorizontalAlignment("right")
           .setBackground("#F5F5F5").setFontColor("#1a1a1a");

      if (item.formula) {
        sheet.getRange(`K${item.row}`).setFormula(item.formula)
             .setHorizontalAlignment("right").setVerticalAlignment("middle");
      } else {
        sheet.getRange(`K${item.row}`).setValue("")
             .setHorizontalAlignment("right").setVerticalAlignment("middle");
      }
    });

    // Apply borders to tax summary
    sheet.getRange("I22:K28").setBorder(true, true, true, true, true, true, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);

    for (let i = 22; i <= 28; i++) {
      sheet.setRowHeight(i, 25);
    }
  } catch (error) {
    console.error('Error creating tax summary section:', error);
  }
}

function CreateBottomSection(sheet) {
  try {
    // Amount in words section (row 29)
    sheet.getRange("A29:H29").merge().setValue("Amount Chargeable (in words):")
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");

    sheet.getRange("I29:K29").merge().setValue("E. & O.E")
         .setFontWeight("bold").setHorizontalAlignment("center")
         .setBackground("#F5F5F5").setFontColor("#1a1a1a");

    // Amount in words value (row 30)
    sheet.getRange("A30:H30").merge()
         .setFormula('=IF(K28<>"",UPPER(NumberToWords(K28)),"")') // Will need to implement NumberToWords
         .setFontWeight("bold").setHorizontalAlignment("left")
         .setVerticalAlignment("top").setWrap(true);

    sheet.getRange("I30:K30").merge().setValue("")
         .setHorizontalAlignment("center");

    // Company's Bank Details (row 31)
    CreateHeaderRow(sheet, 31, "A31:K31", "Company's Bank Details", 11, true, "#F5F5F5", "#1a1a1a", 26);

    // Bank details content (rows 32-34)
    const bankDetails = [
      "A/c Holder's Name: KAVERI TRADERS",
      "Bank Name: ANDHRA BANK    A/c No.: ***************    Branch & IFS Code: ANDB0001234",
      ""
    ];

    bankDetails.forEach((detail, index) => {
      const row = 32 + index;
      sheet.getRange(`A${row}:K${row}`).merge().setValue(detail)
           .setHorizontalAlignment("left").setVerticalAlignment("middle");
    });

    // Declaration section (row 35)
    CreateHeaderRow(sheet, 35, "A35:K35", "Declaration", 11, true, "#F5F5F5", "#1a1a1a", 26);

    // Declaration content (row 36)
    const declaration = "We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.";
    sheet.getRange("A36:K36").merge().setValue(declaration)
         .setHorizontalAlignment("left").setVerticalAlignment("top")
         .setWrap(true);

    // Signature section (row 37)
    sheet.getRange("A37:F37").merge().setValue("")
         .setHorizontalAlignment("left");

    sheet.getRange("G37:K37").merge().setValue("for KAVERI TRADERS\n\n\n\nAuthorised Signatory")
         .setFontWeight("bold").setHorizontalAlignment("center")
         .setVerticalAlignment("bottom").setWrap(true);

    // Apply borders and formatting
    sheet.getRange("A29:K37").setBorder(true, true, true, true, true, true, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);

    sheet.setRowHeight(29, 26);
    sheet.setRowHeight(30, 40);
    sheet.setRowHeight(31, 26);
    sheet.setRowHeight(32, 22);
    sheet.setRowHeight(33, 22);
    sheet.setRowHeight(34, 22);
    sheet.setRowHeight(35, 26);
    sheet.setRowHeight(36, 35);
    sheet.setRowHeight(37, 60);
  } catch (error) {
    console.error('Error creating bottom section:', error);
  }
}

function AutoPopulateInvoiceFields(sheet) {
  try {
    // Auto-populate invoice number with next sequential number
    const nextInvoiceNumber = GetNextInvoiceNumber();
    sheet.getRange("C7").setValue(nextInvoiceNumber)
         .setFontWeight("bold").setFontColor("#DC143C")
         .setHorizontalAlignment("center").setVerticalAlignment("middle");

    // Set current date for Invoice Date and Date of Supply
    const currentDate = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd/MM/yyyy");

    sheet.getRange("C8").setValue(currentDate)
         .setFontWeight("bold").setHorizontalAlignment("left").setVerticalAlignment("middle");

    sheet.getRange("F9").setValue(currentDate)
         .setFontWeight("bold").setHorizontalAlignment("left").setVerticalAlignment("middle");

    sheet.getRange("G9").setValue(currentDate)
         .setFontWeight("bold").setHorizontalAlignment("left").setVerticalAlignment("middle");

    // Reset state code to default
    sheet.getRange("C10").setValue("37")
         .setFontWeight("bold").setHorizontalAlignment("center").setVerticalAlignment("middle");
  } catch (error) {
    console.error('Error auto-populating invoice fields:', error);
  }
}

function SetupTaxCalculationFormulas(sheet) {
  try {
    // Set up formulas for tax calculations in item rows
    for (let row = 18; row <= 21; row++) {
      // Amount = Quantity * Rate
      sheet.getRange(`G${row}`).setFormula(`=IF(AND(D${row}<>"",F${row}<>""),D${row}*F${row},"")`);

      // Taxable Value = Amount
      sheet.getRange(`H${row}`).setFormula(`=G${row}`);

      // IGST Amount = Taxable Value * IGST Rate / 100
      sheet.getRange(`J${row}`).setFormula(`=IF(AND(H${row}<>"",I${row}<>""),H${row}*I${row}/100,"")`);

      // Total Amount = Taxable Value + IGST Amount
      sheet.getRange(`K${row}`).setFormula(`=IF(H${row}<>"",H${row}+J${row},"")`);
    }

    // Set up tax summary formulas
    sheet.getRange("K22").setFormula("=SUM(H18:H21)"); // Total Before Tax
    sheet.getRange("K25").setFormula("=SUM(J18:J21)"); // Total IGST
    sheet.getRange("K27").setFormula("=K25"); // Total Tax Amount
    sheet.getRange("K28").setFormula("=K22+K27"); // Total Amount

    // Set up amount in words formula (will need custom function)
    sheet.getRange("A30").setFormula('=IF(K28<>"",UPPER(NumberToWords(K28)),"")');
  } catch (error) {
    console.error('Error setting up tax calculation formulas:', error);
  }
}

function UpdateMultiItemTaxCalculations(sheet) {
  try {
    // Update summary calculations for multiple items
    sheet.getRange("K22").setFormula("=SUM(H18:H21)"); // Total Before Tax
    sheet.getRange("K25").setFormula("=SUM(J18:J21)"); // Total IGST
    sheet.getRange("K27").setFormula("=K25"); // Total Tax Amount
    sheet.getRange("K28").setFormula("=K22+K27"); // Total Amount

    // Update amount in words
    sheet.getRange("A30").setFormula('=IF(K28<>"",UPPER(NumberToWords(K28)),"")');
  } catch (error) {
    console.error('Error updating multi-item tax calculations:', error);
  }
}

function AutoFillConsigneeFromReceiver(sheet) {
  try {
    // Copy receiver details to consignee section
    const receiverName = sheet.getRange("C12").getValue();
    const receiverAddress = sheet.getRange("C13").getValue();
    const receiverGSTIN = sheet.getRange("C14").getValue();
    const receiverState = sheet.getRange("C15").getValue();
    const receiverStateCode = sheet.getRange("C16").getValue();

    if (receiverName) {
      sheet.getRange("I12").setValue(receiverName);
      sheet.getRange("I13").setValue(receiverAddress);
      sheet.getRange("I14").setValue(receiverGSTIN);
      sheet.getRange("I15").setValue(receiverState);
      sheet.getRange("I16").setValue(receiverStateCode);
    }
  } catch (error) {
    console.error('Error auto-filling consignee from receiver:', error);
  }
}

function AddNewItemRow(sheet) {
  try {
    if (!sheet) {
      sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("GST_Tax_Invoice_for_interstate");
    }

    // Find the last item row (currently supports up to row 21)
    let lastItemRow = 21;

    // Check if we can add more rows (extend the table if needed)
    if (lastItemRow < 25) { // Allow up to 25 rows for items
      const newRow = lastItemRow + 1;
      const srNo = newRow - 17; // Calculate Sr.No.

      // Insert new row and copy formatting from previous row
      sheet.insertRowAfter(lastItemRow);

      // Set Sr.No.
      sheet.getRange(`A${newRow}`).setValue(srNo)
           .setHorizontalAlignment("center").setVerticalAlignment("middle");

      // Set formulas for calculated columns
      sheet.getRange(`G${newRow}`).setFormula(`=IF(AND(D${newRow}<>"",F${newRow}<>""),D${newRow}*F${newRow},"")`);
      sheet.getRange(`H${newRow}`).setFormula(`=G${newRow}`);
      sheet.getRange(`J${newRow}`).setFormula(`=IF(AND(H${newRow}<>"",I${newRow}<>""),H${newRow}*I${newRow}/100,"")`);
      sheet.getRange(`K${newRow}`).setFormula(`=IF(H${newRow}<>"",H${newRow}+J${newRow},"")`);

      // Apply formatting
      sheet.getRange(`A${newRow}:K${newRow}`).setBorder(true, true, true, true, false, false, "#CCCCCC", SpreadsheetApp.BorderStyle.SOLID);
      sheet.setRowHeight(newRow, 28);

      // Update tax summary formulas to include new row
      UpdateMultiItemTaxCalculations(sheet);

      SpreadsheetApp.getUi().alert('Success', 'New item row added successfully!', SpreadsheetApp.getUi().ButtonSet.OK);
    } else {
      SpreadsheetApp.getUi().alert('Limit Reached', 'Maximum number of item rows reached.', SpreadsheetApp.getUi().ButtonSet.OK);
    }
  } catch (error) {
    SpreadsheetApp.getUi().alert('Error', 'Error adding new item row: ' + error.toString(), SpreadsheetApp.getUi().ButtonSet.OK);
  }
}
